import { useState, useEffect } from "react";
import releaseNotesData from "@/data/release-notes.json";

const STORAGE_KEY = "release_notes_read_versions";

interface UseReleaseNotificationsReturn {
  hasUnreadNotifications: boolean;
  currentVersion: string;
  markVersionAsRead: (version: string) => void;
  isVersionRead: (version: string) => boolean;
  clearAllReadVersions: () => void; // Para testes e debug
  readVersions: string[]; // Para debug
  isLoaded: boolean; // Para evitar flash de notificação
}

export const useReleaseNotifications = (): UseReleaseNotificationsReturn => {
  const [readVersions, setReadVersions] = useState<string[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  // Obter a versão atual do aplicativo (primeira versão no array)
  const currentVersion = releaseNotesData.releases && releaseNotesData.releases.length > 0 ? releaseNotesData.releases[0].version : "1.0.0";

  // Carregar versões lidas do localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedVersions = JSON.parse(stored);
        if (Array.isArray(parsedVersions)) {
          setReadVersions(parsedVersions);
        }
      }
      setIsLoaded(true);
    } catch (error) {
      console.error("Erro ao carregar versões lidas do localStorage:", error);
      setReadVersions([]);
      setIsLoaded(true);
    }
  }, []);

  // Salvar versões lidas no localStorage
  const saveReadVersions = (versions: string[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(versions));
      setReadVersions(versions);
    } catch (error) {
      console.error("Erro ao salvar versões lidas no localStorage:", error);
    }
  };

  // Marcar uma versão como lida
  const markVersionAsRead = (version: string) => {
    if (!readVersions.includes(version)) {
      const updatedVersions = [...readVersions, version];
      saveReadVersions(updatedVersions);
    }
  };

  // Verificar se uma versão foi lida
  const isVersionRead = (version: string): boolean => {
    return readVersions.includes(version);
  };

  // Limpar todas as versões lidas (útil para testes)
  const clearAllReadVersions = () => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      setReadVersions([]);
    } catch (error) {
      console.error("Erro ao limpar versões lidas do localStorage:", error);
    }
  };

  // Verificar se há notificações não lidas (versão atual não foi lida)
  // Só calcular após carregar do localStorage
  const hasUnreadNotifications = isLoaded ? !isVersionRead(currentVersion) : false;

  // Debug logs (remover em produção)
  console.log("🔔 Release Notifications Debug:", {
    currentVersion,
    readVersions,
    isLoaded,
    hasUnreadNotifications,
    isCurrentVersionRead: isVersionRead(currentVersion),
  });

  return {
    hasUnreadNotifications,
    currentVersion,
    markVersionAsRead,
    isVersionRead,
    clearAllReadVersions,
    readVersions,
    isLoaded,
  };
};
