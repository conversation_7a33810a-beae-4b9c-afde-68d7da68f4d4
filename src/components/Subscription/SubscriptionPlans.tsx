import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Check, Loader2, CreditCard } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useSubscription } from "@/contexts/SubscriptionProvider";
import { Badge } from "@/components/ui/badge";
import { loadStripe } from "@stripe/stripe-js";

// Chave pública do Stripe
// Chave de teste (para ambiente de desenvolvimento)
// const stripePromise = loadStripe("pk_test_51PkVaQIxqQbmbZWPatjl4a492UAEDJRcaKZCA6qchePR3YAHn7wD7sQlKwLcK9PYXswWuM0afvl8Kc4JP7V7hTO600xQkDjclU");

// Chave de produção (ambiente de produção)
const stripePromise = loadStripe("pk_live_51PkVaQIxqQbmbZWPJiE0vtb8MTOld4WRvyN8zmuXtNoWRSCYO6sfGcCjijeeBLrgwEkwKk4s2JQBCor1alUo17Ly002XmwsQA7");

// IDs dos preços no Stripe
// IDs de teste (para ambiente de desenvolvimento)
// const PRICE_IDS = {
//   individual: "price_1PHxJ4ALvhA5CrMQMKv4Z1F0",
//   shared: "price_1PHxJTALvhA5CrMQfULx4xRQ",
//   business: "price_1PHxJkALvhA5CrMQBPe66nKB",
// };

// IDs de produção (ambiente de produção)
const PRICE_IDS = {
  individual: "price_1RREVbIxqQbmbZWPxURraZMM",
  shared: "price_1RREVhIxqQbmbZWPznPRIxZN",
  business: "price_1RREVoIxqQbmbZWPX8yDVRdX",
};

const plans = [
  {
    id: "individual",
    name: "Individual",
    price: "R$ 19,00",
    description: "Perfeito para uso pessoal",
    features: ["Workspaces ilimitados", "Upload de arquivos ilimitados", "Páginas ilimitadas", "Acesso a todos os recursos"],
    priceId: PRICE_IDS.individual,
  },
  {
    id: "shared",
    name: "Compartilhado",
    price: "R$ 49,00",
    description: "Ideal para pequenas equipes",
    features: ["Tudo do plano Individual", "Até 4 usuários", "R$ 20,00 por usuário adicional"],
    priceId: PRICE_IDS.shared,
  },
  {
    id: "business",
    name: "Empresarial",
    price: "R$ 99,00",
    description: "Para grandes equipes",
    features: ["Tudo do plano Compartilhado", "Até 10 usuários", "R$ 17,00 por usuário adicional"],
    priceId: PRICE_IDS.business,
  },
];

export const SubscriptionPlans = () => {
  const { isSubscribed, isAdmin, checkSubscription } = useSubscription();
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPlan, setCurrentPlan] = useState<string | null>(null);
  const [cancelingPlan, setCancelingPlan] = useState<boolean>(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCurrentPlan();
  }, []);

  const fetchCurrentPlan = async () => {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) return;

      // Usar maybeSingle para evitar erro quando não houver registro
      const { data, error } = await supabase.from("subscribers").select("subscription_tier, subscribed").eq("user_id", session.user.id).maybeSingle();

      if (error) {
        console.error("Erro ao buscar plano:", error);
        return;
      }

      // Verificar se data existe e se a assinatura está ativa
      if (data && data.subscribed) {
        setCurrentPlan(data.subscription_tier);
      } else {
        setCurrentPlan(null);
      }
    } catch (error) {
      console.error("Erro ao buscar plano:", error);
    }
  };

  // Criar subscriber se não existir
  const ensureSubscriberExists = async (userId: string, email: string) => {
    try {
      // Verificar se já existe
      const { data, error } = await supabase.from("subscribers").select("id").eq("user_id", userId).maybeSingle();

      if (error) {
        console.error("Erro ao verificar subscriber:", error);
        return false;
      }

      // Se não existir, criar
      if (!data) {
        const { error: insertError } = await supabase.from("subscribers").insert({
          user_id: userId,
          email: email,
          subscribed: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (insertError) {
          console.error("Erro ao criar subscriber:", insertError);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error("Erro ao garantir existência do subscriber:", error);
      return false;
    }
  };

  const handlePlanSelection = async (planId: string, priceId: string) => {
    try {
      setLoading(true);
      setError(null);
      setSelectedPlan(planId);

      // Se já tem uma assinatura ativa e está tentando mudar de plano, primeiro cancela
      if (isSubscribed && currentPlan && currentPlan !== planId) {
        await cancelSubscription();
      }

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        toast.error("Você precisa estar logado para assinar um plano");
        return;
      }

      // Atualizar o registro com o plano selecionado (mesmo antes de pagar)
      await ensureSubscriberExists(session.user.id, session.user.email);
      await supabase
        .from("subscribers")
        .update({
          subscription_tier: planId,
          updated_at: new Date().toISOString(),
        })
        .eq("user_id", session.user.id);

      // Encontrar o plano selecionado
      const selectedPlanObj = plans.find((p) => p.id === planId);

      if (!selectedPlanObj) {
        throw new Error("Plano não encontrado");
      }

      // Converter o preço de string para número
      // Exemplo: "R$ 19,00" -> 1900
      const amount = parseInt(selectedPlanObj.price.replace("R$ ", "").replace(",", "").replace(".", ""));

      // Usar diretamente o Stripe Checkout para assinaturas
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("Não foi possível carregar o Stripe");
      }

      const { error } = await stripe.redirectToCheckout({
        mode: "subscription",
        lineItems: [{ price: priceId, quantity: 1 }],
        successUrl: `${window.location.origin}/settings/payment/success?plan=${planId}`,
        cancelUrl: `${window.location.origin}/settings`,
        customerEmail: session.user.email,
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error("Erro ao iniciar processo de pagamento:", error);
      setError(error.message || "Ocorreu um erro ao processar sua solicitação");
      toast.error("Erro ao processar assinatura. Tente novamente mais tarde.");
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = async () => {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (session && selectedPlan) {
        // Atualizar o registro de assinante
        await supabase
          .from("subscribers")
          .update({
            subscribed: true,
            subscription_tier: selectedPlan,
            updated_at: new Date().toISOString(),
          })
          .eq("user_id", session.user.id);

        // Recarregar o plano atual
        await fetchCurrentPlan();

        // Atualizar o contexto de assinatura
        await checkSubscription();

        toast.success("Assinatura ativada com sucesso!");
      }
    } catch (error) {
      console.error("Erro ao finalizar assinatura:", error);
      toast.error("Erro ao finalizar assinatura");
    }
  };

  const handlePaymentError = (error: Error) => {
    console.error("Erro no pagamento:", error);
    setError(error.message || "Ocorreu um erro no processamento do pagamento");
    toast.error("Erro no pagamento. Tente novamente.");
  };

  const cancelSubscription = async () => {
    try {
      setCancelingPlan(true);

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        toast.error("Você precisa estar logado para cancelar a assinatura");
        return;
      }

      // Implementação simplificada - apenas marca como não assinado no banco
      const { error } = await supabase
        .from("subscribers")
        .update({
          subscribed: false,
          subscription_end: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          stripe_subscription_id: null,
        })
        .eq("user_id", session.user.id);

      if (error) {
        console.error("Erro ao cancelar assinatura:", error);
        toast.error("Não foi possível cancelar a assinatura");
        return;
      }

      // Atualizar o estado local
      setCurrentPlan(null);

      // Atualizar o contexto de assinatura
      await checkSubscription();

      toast.success("Assinatura cancelada com sucesso");
    } catch (error) {
      console.error("Error canceling subscription:", error);
      toast.error("Erro ao cancelar assinatura");
    } finally {
      setCancelingPlan(false);
    }
  };

  // Verificar parâmetros de URL quando a página carrega (para processar retorno do Stripe)
  useEffect(() => {
    const handleCheckoutReturn = async () => {
      const params = new URLSearchParams(window.location.search);
      const sessionId = params.get("session_id");
      const plan = params.get("plan");
      const additional = params.get("additional");

      // Se há session_id, significa que veio do Stripe Checkout
      if (sessionId) {
        if (plan) {
          setSelectedPlan(plan);
        }

        await handlePaymentSuccess();

        // Limpar parâmetros da URL
        window.history.replaceState({}, document.title, "/settings");
      }
    };

    handleCheckoutReturn();
  }, []);

  const handleAdditionalLicenses = async (planId: string) => {
    try {
      setLoading(true);
      setError(null);

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        toast.error("Você precisa estar logado para adquirir licenças adicionais");
        return;
      }

      // Determinar o preço com base no plano
      const selectedPlanObj = plans.find((p) => p.id === planId);
      if (!selectedPlanObj) {
        throw new Error("Plano não encontrado");
      }

      // Converter o preço de string para número (por usuário adicional)
      let additionalLicensePrice = 0;
      if (planId === "shared") {
        additionalLicensePrice = 2000; // R$ 20,00
      } else if (planId === "business") {
        additionalLicensePrice = 1700; // R$ 17,00
      }

      // Usar diretamente o Stripe Checkout para licenças adicionais
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("Não foi possível carregar o Stripe");
      }

      // Determinar o price ID correto para licenças adicionais
      let additionalPriceId;
      if (planId === "shared") {
        additionalPriceId = PRICE_IDS.additional_license_shared;
      } else if (planId === "business") {
        additionalPriceId = PRICE_IDS.additional_license_business;
      } else {
        throw new Error("Licenças adicionais não disponíveis para este plano");
      }

      const { error } = await stripe.redirectToCheckout({
        mode: "subscription",
        lineItems: [{ price: additionalPriceId, quantity: 1 }],
        successUrl: `${window.location.origin}/settings/payment/success?plan=${planId}&additional=true`,
        cancelUrl: `${window.location.origin}/settings`,
        customerEmail: session.user.email,
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error("Erro ao adicionar licenças:", error);
      setError(error.message || "Ocorreu um erro ao processar sua solicitação");
      toast.error("Erro ao processar licenças adicionais. Tente novamente mais tarde.");
    } finally {
      setLoading(false);
    }
  };

  const renderPlanSelection = () => (
    <div className="grid gap-8 md:grid-cols-3">
      {plans.map((plan) => {
        const isCurrentPlan = currentPlan === plan.id;

        return (
          <Card key={plan.name} className={`flex flex-col ${isCurrentPlan ? "border-primary" : ""}`}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <CardTitle>{plan.name}</CardTitle>
                {isCurrentPlan && <Badge className="bg-primary">Ativo</Badge>}
              </div>
              <CardDescription>{plan.description}</CardDescription>
              <div className="text-3xl font-bold">{plan.price}</div>
              <div className="text-sm text-muted-foreground">por mês</div>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-2">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <Check className="h-4 w-4 mr-2 text-green-500" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              {isCurrentPlan ? (
                <Button variant="outline" className="w-full" onClick={cancelSubscription} disabled={cancelingPlan}>
                  {cancelingPlan ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                  Cancelar Plano
                </Button>
              ) : (
                <>
                  <Button className="w-full" onClick={() => handlePlanSelection(plan.id, plan.priceId)} disabled={loading}>
                    {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                    {currentPlan ? "Mudar para este plano" : "Assinar"}
                  </Button>

                  {/* Botão adicional para adicionar licenças no plano atual */}
                  {isCurrentPlan && plan.id !== "individual" && (
                    <Button variant="outline" className="w-full mt-2" onClick={() => handleAdditionalLicenses(plan.id)} disabled={loading}>
                      Adquirir licenças adicionais
                    </Button>
                  )}
                </>
              )}
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );

  return <div className="container py-8">{renderPlanSelection()}</div>;
};
