import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Task, TaskStatus, TaskDifficulty, TaskType, TaskSubtask, Profile } from "@/types/index";
import { useTasks } from "@/contexts/TasksContext";
import { useAuth } from "@/contexts/AuthContext";
import { useWorkspace } from "@/contexts/WorkspaceContext";
import { Calendar as CalendarIcon, Plus, X, Trash2, Check, Paperclip, Upload, Loader2 } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { isProfilePending, isProfileInactive, canBeAssignedToTask, renderProfileName } from "@/utils/profileUtils";
import { useTaskAttachments } from "@/hooks/useTaskAttachments";
import { TaskAttachmentsList } from "./TaskAttachmentsList";

export interface TaskDialogProps {
  open: boolean;
  onClose: () => void;
  task: Task | null;
  isCreateMode: boolean;
  initialStartDate?: Date;
  initialEndDate?: Date;
  allUsers?: Profile[];
}

export const TaskDialog: React.FC<TaskDialogProps> = ({ open, onClose, task, isCreateMode, initialStartDate, initialEndDate, allUsers = [] }) => {
  const { users, currentUser } = useAuth();
  const { currentWorkspace } = useWorkspace();
  const { addTask, updateTask, addSubtask, updateSubtask, deleteSubtask } = useTasks();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [assigneeId, setAssigneeId] = useState<string | null>(null);
  const [status, setStatus] = useState<TaskStatus>("backlog");
  const [difficulty, setDifficulty] = useState<TaskDifficulty>("medio");
  const [priority, setPriority] = useState<"alta" | "media" | "baixa">("media");
  const [estimatedTime, setEstimatedTime] = useState("1h");
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [category, setCategory] = useState("none");
  const [organization, setOrganization] = useState(currentWorkspace.name);
  const [taskType, setTaskType] = useState<TaskType>(null);
  const [loading, setLoading] = useState(false);
  const [startDateOpen, setStartDateOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);
  const [newSubtaskTitle, setNewSubtaskTitle] = useState("");
  const [subtasks, setSubtasks] = useState<TaskSubtask[]>([]);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [uploading, setUploading] = useState(false);
  const [pendingAttachments, setPendingAttachments] = useState<File[]>([]);

  // Configurar o hook de anexos
  const taskAttachments = useTaskAttachments(task?.id || "", currentWorkspace?.id || "");

  const availableUsers = Array.isArray(allUsers) && allUsers.length > 0 ? allUsers : Array.isArray(users) ? users : [];

  useEffect(() => {
    if (open) {
      if (task) {
        setTitle(task.title || "");
        setDescription(task.description || "");
        setAssigneeId(task.assigneeId || null);
        setStatus(task.status as TaskStatus);
        setDifficulty(task.difficulty as TaskDifficulty);
        setPriority(task.priority || "media");
        setEstimatedTime(task.estimatedTime || "1h");

        try {
          if (task.startDate) {
            const startDateValue = typeof task.startDate === "string" ? new Date(task.startDate) : task.startDate;
            if (!isNaN(startDateValue.getTime())) {
              setStartDate(startDateValue);
            }
          }

          if (task.endDate) {
            const endDateValue = typeof task.endDate === "string" ? new Date(task.endDate) : task.endDate;
            if (!isNaN(endDateValue.getTime())) {
              setEndDate(endDateValue);
            }
          }
        } catch (error) {
          console.error("Erro ao processar datas da tarefa:", error);
          setStartDate(new Date());
          setEndDate(new Date());
        }

        setCategory(task.category || "none");
        setOrganization(task.organization || "");
        setSubtasks(task.subtasks || []);
        setTaskType(task.taskType || null);

        // Carregar anexos se estiver editando uma tarefa existente
        if (task.id) {
          taskAttachments.loadAttachments();
        }
      } else {
        resetForm();
      }
    }
  }, [task, open]);

  useEffect(() => {
    if (isCreateMode && open) {
      if (initialStartDate && !isNaN(initialStartDate.getTime())) {
        setStartDate(initialStartDate);
      }
      if (initialEndDate && !isNaN(initialEndDate.getTime())) {
        setEndDate(initialEndDate);
      }

      if (currentUser?.id) {
        setAssigneeId(currentUser.id);
      }
    }
  }, [initialStartDate, initialEndDate, isCreateMode, currentUser, open]);

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setAssigneeId(currentUser?.id || null);
    setStatus("backlog");
    setDifficulty("medio");
    setPriority("media");
    setEstimatedTime("1h");

    const now = new Date();
    setStartDate(initialStartDate && !isNaN(initialStartDate.getTime()) ? initialStartDate : now);
    setEndDate(initialEndDate && !isNaN(initialEndDate.getTime()) ? initialEndDate : now);

    setCategory("none");
    setOrganization("");
    setSubtasks([]);
    setNewSubtaskTitle("");
    setFormErrors({});
    setTaskType(null);
    setPendingAttachments([]);
  };

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!title.trim()) {
      errors.title = "O título é obrigatório";
    }

    if (!currentWorkspace) {
      errors.workspace = "Nenhum workspace selecionado";
    }

    const isStartDateValid = startDate && !isNaN(startDate.getTime());
    const isEndDateValid = endDate && !isNaN(endDate.getTime());

    if (!isStartDateValid) {
      errors.startDate = "Data de início inválida";
    }

    if (!isEndDateValid) {
      errors.endDate = "Data de término inválida";
    }

    if (isStartDateValid && isEndDateValid && startDate > endDate) {
      errors.dates = "A data inicial não pode ser maior que a data final";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      if (formErrors.title) toast.error(formErrors.title);
      if (formErrors.workspace) toast.error(formErrors.workspace);
      if (formErrors.dates) toast.error(formErrors.dates);
      if (formErrors.startDate) toast.error(formErrors.startDate);
      if (formErrors.endDate) toast.error(formErrors.endDate);
      return;
    }

    if (!currentWorkspace) {
      toast.error("Selecione um workspace antes de salvar a tarefa");
      return;
    }

    setLoading(true);

    try {
      if (isCreateMode) {
        const newTask = await addTask({
          title,
          description,
          assigneeId,
          status,
          difficulty,
          priority,
          startDate,
          endDate,
          estimatedTime,
          category: category || "none",
          organization: organization || undefined,
          workspaceId: currentWorkspace.id,
          subtasks,
          taskType,
        });

        // Mostrar mensagem sobre anexos pendentes
        if (pendingAttachments.length > 0) {
          toast.success(`Tarefa criada com sucesso! Nota: ${pendingAttachments.length} anexo(s) serão enviados quando você editar a tarefa.`);
        } else {
          toast.success("Tarefa criada com sucesso!");
        }
      } else if (task) {
        await updateTask(task.id, {
          title,
          description,
          assigneeId,
          status,
          difficulty,
          priority,
          startDate,
          endDate,
          estimatedTime,
          category: category || "none",
          organization: organization || undefined,
          taskType,
        });
        toast.success("Tarefa atualizada com sucesso!");
      }
      onClose();
    } catch (error) {
      console.error("Erro ao salvar tarefa:", error);
      toast.error("Erro ao salvar tarefa. Tente novamente.");
    } finally {
      setLoading(false);
    }
  };

  const handleAddSubtask = async () => {
    if (!newSubtaskTitle.trim() || !task) return;

    try {
      const result = await addSubtask(task.id, newSubtaskTitle);
      if (result) {
        setSubtasks([...subtasks, result]);
        setNewSubtaskTitle("");
      }
    } catch (error) {
      console.error("Erro ao adicionar subtarefa:", error);
      toast.error("Falha ao adicionar subtarefa");
    }
  };

  const handleToggleSubtask = async (subtaskId: string, completed: boolean) => {
    if (!task) return;

    try {
      await updateSubtask(task.id, subtaskId, { completed });
      setSubtasks((prev) => prev.map((st) => (st.id === subtaskId ? { ...st, completed } : st)));
    } catch (error) {
      console.error("Erro ao atualizar subtarefa:", error);
      toast.error("Falha ao atualizar subtarefa");
    }
  };

  const handleDeleteSubtask = async (subtaskId: string) => {
    if (!task) return;

    try {
      const success = await deleteSubtask(task.id, subtaskId);
      if (success) {
        setSubtasks((prev) => prev.filter((st) => st.id !== subtaskId));
      }
    } catch (error) {
      console.error("Erro ao remover subtarefa:", error);
      toast.error("Falha ao remover subtarefa");
    }
  };

  const statusOptions = [
    { value: "backlog", label: "Backlog" },
    { value: "aguardando", label: "Aguardando" },
    { value: "em_progresso", label: "Em Progresso" },
    { value: "concluido", label: "Concluído" },
  ];

  const difficultyOptions = [
    { value: "facil", label: "Fácil" },
    { value: "medio", label: "Médio" },
    { value: "dificil", label: "Difícil" },
  ];

  const priorityOptions = [
    { value: "alta", label: "Alta" },
    { value: "media", label: "Média" },
    { value: "baixa", label: "Baixa" },
  ];

  const categoryOptions = ["Desenvolvimento", "Design", "Marketing", "Planejamento", "Infraestrutura", "Qualidade", "Suporte"];

  const handleAssigneeChange = (value: string) => {
    setAssigneeId(value === "unassigned" ? null : value);
  };

  // Renderizar nome personalizado para exibição no dropdown
  const renderCustomProfileName = (profile: Profile | undefined) => {
    if (!profile) return "Não atribuído";

    // Se for um nome que inclui "Usuário" com ID, simplificar para apenas "Usuário"
    if (profile.name && profile.name.includes("Usuário")) {
      return "Usuário";
    }

    // Se tiver um nome válido, usar
    if (profile.name && profile.name.trim() !== "") {
      return profile.name;
    }

    // Se tiver email, usar parte antes do @
    if (profile.email && profile.email.trim() !== "") {
      const emailName = profile.email.split("@")[0];
      return emailName.charAt(0).toUpperCase() + emailName.slice(1);
    }

    // Padrão
    return "Usuário";
  };

  // Filtra apenas usuários com nomes válidos, ordenados alfabeticamente
  const getFilteredUsers = () => {
    if (!Array.isArray(availableUsers)) return [];

    console.log(
      "Filtrando e ordenando usuários para atribuição de tarefas:",
      availableUsers.map((u) => ({ id: u.id, name: u.name, displayName: renderCustomProfileName(u) }))
    );

    return availableUsers
      .filter((user) => user && canBeAssignedToTask(user)) // Somente usuários que podem ser atribuídos
      .sort((a, b) => {
        const nameA = renderCustomProfileName(a).toLowerCase();
        const nameB = renderCustomProfileName(b).toLowerCase();
        return nameA.localeCompare(nameB);
      });
  };

  // Função para lidar com o upload de arquivos
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    if (isCreateMode) {
      // No modo de criação, apenas adiciona os arquivos ao estado local
      const newFiles = Array.from(files);
      setPendingAttachments((prev) => [...prev, ...newFiles]);
      toast.success(`${newFiles.length} arquivo(s) adicionado(s). Serão enviados após criar a tarefa.`);
    } else if (task) {
      // No modo de edição, faz upload imediatamente
      setUploading(true);
      try {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          await taskAttachments.uploadAttachment(file);
        }
        // Forçar recarregamento dos anexos para garantir que as URLs sejam atualizadas
        await taskAttachments.loadAttachments();
        toast.success("Anexos enviados com sucesso!");
      } catch (error) {
        console.error("Erro no upload de arquivos:", error);
        toast.error("Falha ao fazer upload de arquivos");
      } finally {
        setUploading(false);
      }
    }

    // Limpar o input para permitir selecionar o mesmo arquivo novamente
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Função para remover um anexo
  const handleRemoveAttachment = async (attachmentId: string) => {
    return await taskAttachments.removeAttachment(attachmentId);
  };

  // Função para remover arquivo pendente (modo criação)
  const handleRemovePendingAttachment = (index: number) => {
    setPendingAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  // Função para acionar o seletor de arquivos
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-notion-sidebar border-notion-border text-notion-text w-[60vw] h-[95vh] max-w-none overflow-hidden flex flex-col">
        <DialogHeader className="shrink-0">
          <DialogTitle>{isCreateMode ? "Cadastro de Tarefas" : "Editar Tarefa"}</DialogTitle>
          <DialogDescription>
            {isCreateMode ? "Preencha os campos abaixo para criar uma nova tarefa." : "Edite os campos da tarefa conforme necessário."}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto py-2">
          <form onSubmit={handleSubmit} className="flex flex-col h-full">
            <div className="flex gap-4 flex-1 overflow-hidden">
              {/* Coluna Esquerda */}
              <div className="flex-1 space-y-4 pr-2 overflow-y-auto">
                <div>
                <Label htmlFor="title" className="text-notion-text">
                  Título<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Título da tarefa"
                  className={`bg-notion-page border-notion-border text-notion-text ${formErrors.title ? "border-red-500" : ""}`}
                  required
                />
                {formErrors.title && <p className="text-red-500 text-sm mt-1">{formErrors.title}</p>}
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="assignee" className="text-notion-text">
                    Responsável pela tarefa
                  </Label>
                  <Select value={assigneeId || "unassigned"} onValueChange={handleAssigneeChange}>
                    <SelectTrigger className="bg-notion-page border-notion-border text-notion-text">
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-sidebar border-notion-border max-h-[300px]">
                      <SelectItem value="unassigned" className="text-notion-text">
                        Não atribuído
                      </SelectItem>
                      {getFilteredUsers().map((user) => (
                        <SelectItem key={user.id} value={user.id} className="text-notion-text">
                          {renderCustomProfileName(user)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="estimatedTime" className="text-notion-text">
                    Tempo estimado
                  </Label>
                  <Input
                    id="estimatedTime"
                    placeholder="Ex: 2h"
                    value={estimatedTime}
                    onChange={(e) => setEstimatedTime(e.target.value)}
                    className="bg-notion-page border-notion-border text-notion-text"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate" className="text-notion-text">
                    Previsão de início
                  </Label>
                  <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal bg-notion-page border-notion-border text-notion-text",
                          !startDate && "text-notion-muted"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, "dd/MM/yyyy", { locale: ptBR }) : <span>Selecione uma data</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-notion-page border-notion-border pointer-events-auto" align="start">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={(date) => {
                          if (date) {
                            setStartDate(date);
                            setStartDateOpen(false);

                            if (endDate < date) {
                              setEndDate(date);
                            }
                          }
                        }}
                        initialFocus
                        className="p-3 pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                  {formErrors.startDate && <p className="text-red-500 text-sm mt-1">{formErrors.startDate}</p>}
                </div>

                <div>
                  <Label htmlFor="endDate" className="text-notion-text">
                    Previsão de término
                  </Label>
                  <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal bg-notion-page border-notion-border text-notion-text",
                          !endDate && "text-notion-muted"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, "dd/MM/yyyy", { locale: ptBR }) : <span>Selecione uma data</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-notion-page border-notion-border pointer-events-auto" align="start">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={(date) => {
                          if (date) {
                            setEndDate(date);
                            setEndDateOpen(false);
                          }
                        }}
                        initialFocus
                        fromDate={startDate}
                        className="p-3 pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                  {formErrors.endDate && <p className="text-red-500 text-sm mt-1">{formErrors.endDate}</p>}
                </div>
                {formErrors.dates && <p className="text-red-500 text-sm col-span-2 -mt-2">{formErrors.dates}</p>}
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="difficulty" className="text-notion-text">
                    Dificuldade
                  </Label>
                  <Select value={difficulty} onValueChange={(value) => setDifficulty(value as TaskDifficulty)}>
                    <SelectTrigger className="bg-notion-page border-notion-border text-notion-text">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-sidebar border-notion-border">
                      {difficultyOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="priority" className="text-notion-text">
                    Prioridade
                  </Label>
                  <Select value={priority} onValueChange={(value) => setPriority(value as "alta" | "media" | "baixa")}>
                    <SelectTrigger className="bg-notion-page border-notion-border text-notion-text">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-sidebar border-notion-border">
                      {priorityOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="status" className="text-notion-text">
                    Status
                  </Label>
                  <Select value={status} onValueChange={(value) => setStatus(value as TaskStatus)}>
                    <SelectTrigger className="bg-notion-page border-notion-border text-notion-text">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-sidebar border-notion-border">
                      {statusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="organization" className="text-notion-text">
                    Organização
                  </Label>
                  <Input
                    id="organization"
                    placeholder={`Ex: ${currentWorkspace.name}`}
                    value={organization}
                    onChange={(e) => setOrganization(e.target.value)}
                    className="bg-notion-page border-notion-border text-notion-text"
                  />
                </div>

                <div>
                  <Label htmlFor="category" className="text-notion-text">
                    Categoria
                  </Label>
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger className="bg-notion-page border-notion-border text-notion-text">
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-sidebar border-notion-border">
                      <SelectItem value="none">Sem categoria</SelectItem>
                      {categoryOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="taskType" className="text-notion-text">
                    Tipo de Tarefa
                  </Label>
                  <Select value={taskType || "none"} onValueChange={(value) => setTaskType(value === "none" ? null : (value as TaskType))}>
                    <SelectTrigger className="bg-notion-page border-notion-border text-notion-text">
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-sidebar border-notion-border">
                      <SelectItem value="none">Sem tipo</SelectItem>
                      <SelectItem value="bug">🐞 Bug</SelectItem>
                      <SelectItem value="nova_funcionalidade">✨ Nova Funcionalidade</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Campo Descrição movido para baixo */}
              <div>
                <Label htmlFor="description" className="text-notion-text mb-2">
                  Descrição
                </Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Descreva a tarefa..."
                  className="bg-notion-page border-notion-border text-notion-text min-h-[120px]"
                />
                </div>

              </div>

              {/* Divisor vertical */}
              <Separator orientation="vertical" className="h-auto mx-1" />

              {/* Coluna Direita - Apenas Subtarefas e Anexos */}
              <div className="w-80 space-y-4 pl-2 overflow-y-auto">
                {!isCreateMode && task && (
                  <div>
                    <Label className="text-notion-text mb-2 block">Subtarefas</Label>

                    <div className="space-y-2 mb-2 max-h-[200px] overflow-y-auto pr-2">
                      {subtasks.map((subtask) => (
                        <div key={subtask.id} className="flex items-center gap-2 bg-notion-page p-2 rounded-md">
                          <Checkbox
                            id={`subtask-${subtask.id}`}
                            checked={subtask.completed}
                            onCheckedChange={(checked) => handleToggleSubtask(subtask.id, checked === true)}
                            className="data-[state=checked]:bg-green-600"
                          />
                          <label
                            htmlFor={`subtask-${subtask.id}`}
                            className={`flex-1 text-sm ${subtask.completed ? "line-through text-notion-muted" : "text-notion-text"}`}
                          >
                            {subtask.title}
                          </label>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteSubtask(subtask.id)}
                            className="h-6 w-6 text-notion-muted hover:text-red-500"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>

                    <div className="flex items-center gap-2">
                      <Input
                        value={newSubtaskTitle}
                        onChange={(e) => setNewSubtaskTitle(e.target.value)}
                        placeholder="Adicionar subtarefa"
                        className="bg-notion-page border-notion-border text-notion-text"
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            handleAddSubtask();
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="bg-notion-page border-notion-border text-notion-text"
                        onClick={handleAddSubtask}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Seção de Anexos - Disponível tanto para criação quanto edição */}
                <div className="mt-4">
                  <div className="flex justify-between items-center mb-2">
                    <Label className="text-notion-text">Anexos</Label>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={triggerFileInput}
                      disabled={uploading}
                      className="bg-notion-page border-notion-border text-notion-text h-8 px-2 py-1"
                    >
                      {uploading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          <span>Enviando...</span>
                        </>
                      ) : (
                        <>
                          <Paperclip className="h-4 w-4 mr-1" />
                          <span>Anexar</span>
                        </>
                      )}
                    </Button>
                    <input type="file" ref={fileInputRef} onChange={handleFileUpload} className="hidden" multiple />
                  </div>

                  {/* Lista de Anexos */}
                  {isCreateMode ? (
                    <div className="space-y-2">
                      {pendingAttachments.length > 0 ? (
                        <div className="space-y-2">
                          <p className="text-notion-muted text-sm">Arquivos selecionados:</p>
                          <div className="max-h-[200px] overflow-y-auto space-y-2">
                            {pendingAttachments.map((file, index) => (
                              <div key={index} className="flex items-center justify-between p-2 bg-notion-page rounded-md border border-notion-border">
                                <div className="flex items-center gap-2">
                                  <Paperclip className="h-4 w-4 text-notion-muted" />
                                  <span className="text-sm text-notion-text truncate">{file.name}</span>
                                  <span className="text-xs text-notion-muted">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemovePendingAttachment(index)}
                                  className="h-6 w-6 p-0 text-notion-muted hover:text-red-500"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="text-notion-muted text-sm italic p-4 bg-notion-page rounded-md border border-dashed border-notion-border text-center">
                          Clique em "Anexar" para adicionar arquivos à tarefa.
                        </div>
                      )}
                    </div>
                  ) : !taskAttachments.loading ? (
                    <div className="max-h-[200px] overflow-y-auto">
                      <TaskAttachmentsList attachments={taskAttachments.attachments} onRemove={handleRemoveAttachment} />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-20 bg-notion-page rounded-md">
                      <Loader2 className="h-5 w-5 text-notion-muted animate-spin mr-2" />
                      <span className="text-notion-muted text-sm">Carregando anexos...</span>
                    </div>
                  )}
                </div>

                {isCreateMode && (
                  <div className="text-notion-muted text-sm italic mt-4">
                    Após criar a tarefa, você poderá adicionar subtarefas.
                  </div>
                )}
              </div>
            </div>
            
            {/* Footer fixo na parte inferior */}
            <DialogFooter className="mt-6 pt-4 border-t border-notion-border shrink-0">
              <Button type="button" variant="destructive" onClick={onClose} className="bg-red-600 hover:bg-red-700" disabled={loading}>
                Cancelar
              </Button>
              <Button type="submit" variant="default" className="bg-green-600 hover:bg-green-700" disabled={loading}>
                {loading ? "Salvando..." : "Salvar"}
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};
