import React, { useEffect, useState } from "react";
import { Di<PERSON>, <PERSON><PERSON>Content, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import releaseNotesData from "@/data/release-notes.json";

interface ReleaseNotesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface Release {
  version: string;
  date: string;
  corrections: string[];
  features: string[];
}

export const ReleaseNotesModal: React.FC<ReleaseNotesModalProps> = ({ open, onOpenChange }) => {
  const [releases, setReleases] = useState<Release[]>([]);

  useEffect(() => {
    setReleases(releaseNotesData.releases || []);
  }, []);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle><PERSON><PERSON> de versão</DialogTitle>
          <DialogDescription>Acompanhe as novidades e correções do sistema</DialogDescription>
        </DialogHeader>
        <div className="space-y-6 max-h-[60vh] overflow-y-auto p-1">
          {releases.map((release) => (
            <div key={release.version} className="space-y-3 border-b border-gray-200 pb-4 last:border-0">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Versão {release.version}</h3>
                <span className="text-sm text-gray-500">{new Date(release.date).toLocaleDateString("pt-BR")}</span>
              </div>

              {release.corrections.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-gray-700">Correções:</h4>
                  <ul className="list-disc ml-5 text-sm space-y-1">
                    {release.corrections.map((correction, index) => (
                      <li key={index}>{correction}</li>
                    ))}
                  </ul>
                </div>
              )}

              {release.features.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-gray-700">Novas funcionalidades:</h4>
                  <ul className="list-disc ml-5 text-sm space-y-1">
                    {release.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
